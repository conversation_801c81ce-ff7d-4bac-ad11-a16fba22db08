<template>
  <div
    :id="id"
    class="ui-map"
    :style="{
      width: width,
      height: height,
    }"
  >
    <div :id="`${id}--map`" style="width: 100%; height: 100%"></div>
    <div id="map-tooltip"></div>
    <Search
      v-if="searchbox"
      v-model:value="searchVal"
      class="map-search"
      width="300px"
      :placeholder="
        lat?.toFixed && lon?.toFixed
          ? lat.toFixed(6) + ',' + lon.toFixed(6)
          : 'Cari...'
      "
      :value-as-object="true"
      text-key="display_name"
      :dbref="{ method: 'get', url: parseUrl, parseData: parseData }"
    />
  </div>
</template>
<script>
import 'leaflet/dist/leaflet.css'
import L from 'leaflet'
import { markRaw } from 'vue'
import Search from './Search.vue'

// Fix for default markers in Leaflet with webpack
delete L.Icon.Default.prototype._getIconUrl
L.Icon.Default.mergeOptions({
  iconRetinaUrl:
    'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-icon-2x.png',
  iconUrl:
    'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-icon.png',
  shadowUrl:
    'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-shadow.png',
})

export default {
  components: {
    Search,
  },
  props: {
    width: {
      type: String,
      default: '250px',
    },
    height: {
      type: String,
      default: '250px',
    },
    lat: {
      type: [String, Number],
      default: null,
    },
    lon: {
      type: [String, Number],
      default: null,
    },
    searchbox: {
      type: Boolean,
      default: true,
    },
    markers: {
      type: Array,
      default: () => [],
    },
    geojson: {
      type: Object,
      default: null,
    },
    disabled: {
      type: Boolean,
      default: false,
    },
  },
  emits: [
    'update:lat',
    'update:lon',
    'change',
    'ready',
    'marker-click',
    'marker-hover',
  ],
  data() {
    return {
      map: null,
      id: null,
      checker: null,
      searchVal: null,
      value: null,
      hoverElement: null,
      oldValue: {
        lat: null,
        lon: null,
      },
      tooltipOverlay: null,
      xMarkers: {},
      markerLayers: {},
      geojsonLayer: null,
    }
  },
  watch: {
    searchVal(val) {
      console.log('watch searchVal', val)
      if (val) {
        this.map.setView([parseFloat(val.lat), parseFloat(val.lon)], 13)
        this.$emit('update:lat', val.lat)
        this.$emit('update:lon', val.lon)
      }
    },
    markers(val) {
      this.clearMarkers()
      val.forEach((m) => {
        this.addMarker(m)
      })
      this.$emit('change', this.map)
    },
    geojson(val) {
      if (val) {
        this.drawGeoJson()
      }
    },
    value(val) {
      // var l = val.split("|");
      let tooltip = document.getElementById('map-tooltip')
      tooltip.style.visibility = 'hidden'
      setTimeout(() => {
        tooltip.style.visibility = 'visible'
      }, 700)

      this.clearMarkers()
      if (val && val.lat && val.lon) {
        this.addMarker(val)
        // Animate to new position
        this.map.flyTo(
          [parseFloat(val.lat), parseFloat(val.lon)],
          this.map.getZoom(),
          {
            duration: 0.7,
          }
        )
        this.$emit('update:lat', val.lat)
        this.$emit('update:lon', val.lon)
        if (this.oldValue.lat != val.lat || this.oldValue.lon != val.lon)
          this.$emit('change', this.map)
        this.oldValue = {
          lat: val.lat,
          lon: val.lon,
        }
      }
    },
    lat(val) {
      this.value = { lat: val, lon: this.lon }
    },
  },
  async created() {
    if (!window.uuid) window.uuid = 0
    this.id = 'map-' + window.uuid++
  },
  mounted() {
    // Initialize Leaflet map
    // Convert OpenLayers center coordinates to lat/lng (approximately Indonesia)
    const map = L.map(this.id + '--map').setView([-7.25, 112.75], 9)

    // Mark the map as non-reactive to avoid Vue reactivity issues
    this.map = markRaw(map)

    // Add OpenStreetMap tile layer
    L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
      attribution: '© OpenStreetMap contributors',
    }).addTo(this.map)

    // Handle map clicks
    this.map.on('click', (evt) => {
      if (this.disabled) return

      // Check if click was on a marker
      let clickedOnMarker = false
      this.map.eachLayer((layer) => {
        if (
          layer instanceof L.Marker &&
          layer.getLatLng().distanceTo(evt.latlng) < 50
        ) {
          clickedOnMarker = true
          // Trigger marker click event
          if (layer.markerData) {
            this.markerClick(layer.markerData)
          }
        }
      })

      // If not clicked on marker, set new position
      if (!clickedOnMarker) {
        this.value = { lat: evt.latlng.lat, lon: evt.latlng.lng }
      }
    })

    // Handle mouse move for hover effects
    this.map.on('mousemove', (evt) => {
      let hoveredMarker = null
      this.map.eachLayer((layer) => {
        if (
          layer instanceof L.Marker &&
          layer.getLatLng().distanceTo(evt.latlng) < 50
        ) {
          hoveredMarker = layer
        }
      })

      if (hoveredMarker && this.hoverElement !== hoveredMarker) {
        this.hoverElement = hoveredMarker
        this.map.getContainer().style.cursor = 'pointer'
        if (hoveredMarker.markerData) {
          this.markerHover(hoveredMarker.markerData)
        }
      } else if (!hoveredMarker && this.hoverElement) {
        this.hoverElement = null
        this.map.getContainer().style.cursor = ''
        this.markerHover(null)
      }
    })

    this.checker = setInterval(this.refreshIfNotExist.bind(this), 1000)
    if (this.geojson) this.drawGeoJson()
  },
  beforeUnmount() {
    // Clean up the map and intervals
    if (this.checker) {
      clearInterval(this.checker)
    }
    if (this.map) {
      this.map.remove()
    }
  },
  methods: {
    refreshIfNotExist() {
      if (document.querySelector(`#${this.id}--map .leaflet-map-pane`)) {
        clearInterval(this.checker)
        this.$emit('ready', this.map)
      } else {
        this.map.invalidateSize()
        this.value = {
          lat: this.lat,
          lon: this.lon,
        }
      }
    },
    parseData(ret) {
      return ret.data
    },
    parseUrl(keyword) {
      return `https://nominatim.openstreetmap.org/search?q=${keyword}&format=json&countrycodes=id`
    },
    setTooltip(str, opts) {
      let tooltip = document.getElementById('map-tooltip')
      tooltip.innerHTML = str
      if (!str) {
        tooltip.style.display = 'none'
        return
      } else {
        tooltip.style.display = ''
      }
      if (opts?.style) {
        for (let key in opts.style) {
          tooltip.style[key] = opts.style[key]
        }
      }
    },

    // MARKERS
    clearMarkers() {
      // Remove all marker layers from map
      Object.values(this.markerLayers).forEach((layerGroup) => {
        this.map.removeLayer(layerGroup)
      })
      this.markerLayers = {}
      this.xMarkers = {}
    },
    getMarkers() {
      return this.xMarkers
    },
    markerClick(data) {
      this.$emit('marker-click', data)
    },
    markerHover(data) {
      this.$emit('marker-hover', data)
    },
    addMarker(m) {
      // Create Leaflet marker
      let icon = null

      if (typeof m.icon == 'object') {
        // Custom icon object
        icon = L.icon(m.icon)
      } else {
        // Create icon from iconSrc or use default
        if (m.iconSrc) {
          icon = L.icon({
            iconUrl: m.iconSrc,
            iconSize: [25, 25],
            iconAnchor: [12, 25],
            popupAnchor: [0, -25],
          })
        } else {
          // Use default Leaflet marker
          icon = new L.Icon.Default()
        }
      }

      const marker = L.marker([parseFloat(m.lat), parseFloat(m.lon)], { icon })

      // Store marker data for click/hover events
      marker.markerData = m.title
      marker.onclick = m.onclick

      let layerName = 'markers' + (m.layer ? '-' + m.layer : '')

      // Get or create layer group for this marker layer
      if (!this.markerLayers[layerName]) {
        this.markerLayers[layerName] = markRaw(L.layerGroup().addTo(this.map))
        this.xMarkers[layerName] = []
      }

      // Add marker to layer group
      this.markerLayers[layerName].addLayer(markRaw(marker))
      this.xMarkers[layerName].push(markRaw(marker))
    },
    getLayer(name) {
      return this.markerLayers[name] || this.geojsonLayer
    },
    removeLayer(name) {
      if (name === 'geojson-layer' && this.geojsonLayer) {
        this.map.removeLayer(this.geojsonLayer)
        this.geojsonLayer = null
      } else if (this.markerLayers[name]) {
        this.map.removeLayer(this.markerLayers[name])
        delete this.markerLayers[name]
        delete this.xMarkers[name]
      }
    },
    drawGeoJson() {
      this.removeLayer('geojson-layer')

      if (!this.geojson) return

      // Create GeoJSON layer with Leaflet
      this.geojsonLayer = markRaw(
        L.geoJSON(this.geojson, {
          style: () => {
            return {
              color: this.geojson?.style?.color || 'blue',
              weight: 2,
              opacity: 1,
              dashArray: '5, 5',
              fillColor:
                this.geojson?.style?.fillColor || 'rgba(0, 0, 255, 0.1)',
              fillOpacity: 0.1,
            }
          },
        }).addTo(this.map)
      )
    },
  },
}
</script>
<style lang="scss">
.ui-map {
  position: relative;
  .map-search {
    position: absolute;
    right: 10px;
    top: 10px;
    z-index: 1000;

    input {
      background: white;
      padding: 8px 12px;
    }
    input:focus {
      background: white;
    }
  }
  // Hide Leaflet attribution if needed
  .leaflet-control-attribution {
    display: none;
  }
  #map-tooltip {
    background: rgba(255, 255, 255, 0.7);
    font-size: small;
    border-radius: 5px;
    padding: 4px 8px;
    border: 1px solid white;
    position: absolute;
    right: 10px;
    bottom: 10px;
    z-index: 1000;
  }
}
</style>
