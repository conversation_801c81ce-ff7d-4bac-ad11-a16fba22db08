<template>
  <div
    :id="id"
    class="ui-map"
    :style="{
      width: width,
      height: height,
    }"
  >
    <div :id="`${id}--map`" style="width: 100%; height: 100%"></div>
    <div id="map-tooltip"></div>
    <Search
      v-if="searchbox"
      v-model:value="searchVal"
      class="map-search"
      width="300px"
      :placeholder="
        lat?.toFixed && lon?.toFixed
          ? lat.toFixed(6) + ',' + lon.toFixed(6)
          : 'Cari...'
      "
      :value-as-object="true"
      text-key="display_name"
      :dbref="{ method: 'get', url: parseUrl, parseData: parseData }"
    />
  </div>
</template>
<script>
import 'ol/ol.css'
import { Map, View, Feature } from 'ol'
import { fromLonLat, toLonLat } from 'ol/proj'
import {
  Icon as MapIcon,
  Style as MapStyle,
  Fill,
  Stroke,
  Style,
} from 'ol/style'
import { Point as MapPoint } from 'ol/geom'
import Overlay from 'ol/Overlay'
import TileLayer from 'ol/layer/Tile'
import MapVector from 'ol/source/Vector'
import LayerVector from 'ol/layer/Vector'
import OSM from 'ol/source/OSM'
import GeoJSON from 'ol/format/GeoJSON.js'
import Search from './Search.vue'

export default {
  components: {
    Search,
  },
  props: {
    width: {
      type: String,
      default: '250px',
    },
    height: {
      type: String,
      default: '250px',
    },
    lat: [String, Number],
    lon: [String, Number],
    searchbox: {
      type: Boolean,
      default: true,
    },
    markers: Array,
    geojson: {
      type: Object,
      default: null,
    },
    disabled: {
      type: Boolean,
      default: false,
    },
  },
  data: () => ({
    map: null,
    mapView: null,
    id: null,
    checker: null,
    searchVal: null,
    value: null,
    hoverElement: null,
    oldValue: {
      lat: null,
      lon: null,
    },
    tooltipOverlay: null,
    xMarkers: {},
  }),
  watch: {
    searchVal(val) {
      console.log('watch searchVal', val)
      if (val) {
        this.mapView.setCenter(
          fromLonLat([parseFloat(val.lon), parseFloat(val.lat)])
        )
        this.mapView.setZoom(13)
        this.$emit('update:lat', val.lat)
        this.$emit('update:lon', val.lon)
      }
    },
    markers(val) {
      this.clearMarkers()
      val.forEach((m) => {
        this.addMarker(m)
      })
      this.$emit('change', this.map)
    },
    geojson(val) {
      if (val) {
        this.drawGeoJson()
      }
    },
    value(val) {
      // var l = val.split("|");
      let tooltip = document.getElementById('map-tooltip')
      tooltip.style.visibility = 'hidden'
      setTimeout(() => {
        tooltip.style.visibility = 'visible'
      }, 700)

      this.clearMarkers()
      if (val && val.lat && val.lon) {
        this.addMarker(val)
        // let zoom = this.mapView.getZoom()
        this.mapView.animate({
          center: fromLonLat([parseFloat(val.lon), parseFloat(val.lat)]),
          // zoom: zoom < 13 ? 13 : zoom,
          duration: 700,
        })
        this.$emit('update:lat', val.lat)
        this.$emit('update:lon', val.lon)
        if (this.oldValue.lat != val.lat || this.oldValue.lon != val.lon)
          this.$emit('change', this.map)
        this.oldValue = {
          lat: val.lat,
          lon: val.lon,
        }
      }
    },
    lat(val) {
      this.value = { lat: val, lon: this.lon }
    },
  },
  async created() {
    if (!window.uuid) window.uuid = 0
    this.id = 'map-' + window.uuid++
  },
  mounted() {
    this.mapView = new View({
      center: [12285696.496001672, -808222.1359253724],
      zoom: 9,
    })
    this.map = new Map({
      target: this.id + '--map',
      layers: [
        new TileLayer({
          source: new OSM(),
        }),
      ],
      view: this.mapView,
    })
    this.map.on('click', (evt) => {
      var feature = this.map.forEachFeatureAtPixel(
        evt.pixel,
        function (feature) {
          return feature
        }
      )
      if (feature) {
        // console.log(feature.values_.data)
        // var coordinates = feature.getGeometry().getCoordinates();
        // if (feature.N.onclick) feature.N.onclick(feature.N.tooltip_data);
        this.markerClick(feature.values_?.data)
        if (!feature.values_?.data) {
          if (this.disabled) return
          let lonlat = toLonLat(evt.coordinate)
          this.value = { lat: lonlat[1], lon: lonlat[0] }
        }
      } else {
        if (this.disabled) return
        let lonlat = toLonLat(evt.coordinate)
        this.value = { lat: lonlat[1], lon: lonlat[0] }
      }
    })
    this.map.on('pointermove', (evt) => {
      var feature = this.map.forEachFeatureAtPixel(
        evt.pixel,
        function (feature) {
          return feature
        }
      )
      if (feature) {
        // if (this.tooltip) this.tooltip.style.display = ''
        if (this.hoverElement != feature) {
          this.hoverElement = feature

          // console.log(feature.values_.data)
          this.map.getViewport().style.cursor = 'pointer'
          this.markerHover(feature.values_.data)
        }
      } else {
        // if (this.tooltip) this.tooltip.style.display = 'none'
        if (this.hoverElement != null) {
          this.hoverElement = null
          this.map.getViewport().style.cursor = ''
          this.markerHover(null)
        }
      }
    })

    this.checker = setInterval(this.refreshIfNotExist.bind(this), 1000)
    if (this.geojson) this.drawGeoJson()
  },
  methods: {
    refreshIfNotExist() {
      if (document.querySelector(`#${this.id}--map .ol-layers canvas`)) {
        clearInterval(this.checker)
        this.$emit('ready', this.map)
      } else {
        this.map.updateSize()
        this.value = {
          lat: this.lat,
          lon: this.lon,
        }
      }
    },
    parseData(ret) {
      return ret.data
    },
    parseUrl(keyword) {
      return `https://nominatim.openstreetmap.org/search?q=${keyword}&format=json&countrycodes=id`
    },
    setTooltip(str, opts) {
      let tooltip = document.getElementById('map-tooltip')
      tooltip.innerHTML = str
      if (!str) {
        tooltip.style.display = 'none'
        return
      } else {
        tooltip.style.display = ''
      }
      if (opts?.style) {
        for (let key in opts.style) {
          tooltip.style[key] = opts.style[key]
        }
      }

      if (!this.tooltipOverlay) {
        // this.tooltipOverlay = new Overlay({
        //   element: tooltip,
        //   offset: [10, 0],
        //   positioning: 'bottom-left',
        // })
        // this.map.addOverlay(this.tooltipOverlay)
      }
      // let k = Object.keys(this.xMarkers['markers']?.featuresRtree_?.items_)
      // if (k) {
      //   let coord = this.xMarkers['markers']?.featuresRtree_?.items_[k[0]]
      //   this.tooltipOverlay.setPosition([coord.maxX, coord.maxY])
      // }
    },

    // MARKERS
    clearMarkers() {
      if (this.xMarkers['markers']) {
        this.xMarkers['markers'].clear()
      }
    },
    getMarkers() {
      return this.xMarkers
    },
    markerClick(data) {
      this.$emit('marker-click', data)
    },
    markerHover(data) {
      this.$emit('marker-hover', data)
    },
    addMarker(m) {
      var marker = new Feature({
        data: m.title,
        onclick: m.onclick,
        geometry: new MapPoint(
          fromLonLat([parseFloat(m.lon), parseFloat(m.lat)])
        ),
      })
      if (typeof m.icon == 'object') {
        marker.setStyle(
          new MapStyle({
            image: new MapIcon(m.icon),
          })
        )
      } else {
        marker.setStyle(
          new MapStyle({
            image: new MapIcon({
              color: m.icon || 'white',
              crossOrigin: 'anonymous',
              src: m.iconSrc || '/img/icons/dot.png',
            }),
          })
        )
      }

      let markerName = 'markers' + (m.layer ? '-' + m.layer : '')
      var markers = this.xMarkers[markerName]
      if (!markers) {
        // if (this.attr("tooltip")) {
        //   $(this).UI("createTooltip");
        //   this.map.on("pointermove", $.UI.map.showTooltip.bind(this));
        // }

        var markerVectorLayer = new LayerVector({
          name: 'marker-' + (m.layer ? m.layer : 'default'),
          source: new MapVector({
            features: [marker],
          }),
        })
        this.map.addLayer(markerVectorLayer)
        markers = markerVectorLayer.getSource()
      } else {
        markers.addFeature(marker)
      }
      this.xMarkers[markerName] = markers
      // console.log(markerName, this.xMarkers);
      // this.data("markers" + (layerName ? "-" + layerName : ""), markers);
    },
    getLayer(name) {
      let layer = null
      this.map.getLayers().forEach((l) => {
        if (l.values_.name == name) layer = l
      })
      return layer
    },
    removeLayer(name) {
      let layer = this.getLayer(name)
      if (layer) this.map.removeLayer(layer)
      if (this.xMarkers[name]) delete this.xMarkers[name]
    },
    drawGeoJson() {
      this.removeLayer('geojson-layer')
      const vectorSource = new MapVector({
        features: new GeoJSON().readFeatures(this.geojson),
      })
      const styles = {
        Polygon: new Style({
          stroke: new Stroke({
            color: this.geojson?.style?.color || 'blue',
            lineDash: [3],
            width: 2,
          }),
          fill: new Fill({
            color: this.geojson?.style?.fillColor || 'rgba(0, 0, 255, 0.1)',
          }),
        }),
      }
      const geojsonLayer = new LayerVector({
        name: 'geojson-layer',
        source: vectorSource,
        style: (feature) => {
          return styles[feature.getGeometry().getType()]
        },
      })
      this.map.addLayer(geojsonLayer)
      // geojsonLayer.setZIndex(0)
      // this.mapView.setLayerIndex(geojsonLayer, 0)
    },
  },
}
</script>
<style lang="scss">
.ui-map {
  position: relative;
  .map-search {
    position: absolute;
    right: 10px;
    top: 10px;

    input {
      background: white;
      padding: 8px 12px;
    }
    input:focus {
      background: white;
    }
  }
  .ol-attribution {
    display: none;
  }
  #map-tooltip {
    background: rgba(255, 255, 255, 0.7);
    font-size: small;
    border-radius: 5px;
    padding: 4px 8px;
    border: 1px solid white;
    position: absolute;
    right: 10px;
    bottom: 10px;
  }
}
</style>
