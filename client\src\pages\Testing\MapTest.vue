<template>
  <div style="padding: 20px;">
    <h2>Leaflet Map Component Test</h2>

    <div style="margin-bottom: 20px;">
      <h3>Basic Map Test</h3>
      <Map
        width="800px"
        height="400px"
        :lat="-7.25"
        :lon="112.75"
        @ready="onMapReady"
        @change="onMapChange"
        @marker-click="onMarkerClick"
        @marker-hover="onMarkerHover"
      />
    </div>

    <div style="margin-bottom: 20px;">
      <h3>Map with Markers</h3>
      <Map
        width="800px"
        height="400px"
        :lat="-7.25"
        :lon="112.75"
        :markers="testMarkers"
        @ready="onMapReady"
        @change="onMapChange"
        @marker-click="onMarkerClick"
        @marker-hover="onMarkerHover"
      />
    </div>

    <div style="margin-bottom: 20px;">
      <h3>Map with GeoJSON</h3>
      <Map
        width="800px"
        height="400px"
        :lat="-7.25"
        :lon="112.75"
        :geojson="testGeojson"
        @ready="onMapReady"
        @change="onMapChange"
        @marker-click="onMarkerClick"
        @marker-hover="onMarkerHover"
      />
    </div>

    <div style="margin-top: 20px;">
      <h3>Events Log</h3>
      <div style="background: #f5f5f5; padding: 10px; height: 200px; overflow-y: auto;">
        <div v-for="(event, index) in events" :key="index">
          {{ event }}
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  data() {
    return {
      events: [],
      testMarkers: [
        {
          lat: -7.25,
          lon: 112.75,
          title: 'Test Marker 1',
          iconSrc: '/img/icons/dot.png'
        },
        {
          lat: -7.26,
          lon: 112.76,
          title: 'Test Marker 2',
          iconSrc: '/img/icons/dot.png'
        }
      ],
      testGeojson: {
        type: 'FeatureCollection',
        style: {
          color: 'red',
          fillColor: 'rgba(255, 0, 0, 0.2)'
        },
        features: [
          {
            type: 'Feature',
            geometry: {
              type: 'Polygon',
              coordinates: [[
                [112.74, -7.24],
                [112.76, -7.24],
                [112.76, -7.26],
                [112.74, -7.26],
                [112.74, -7.24]
              ]]
            }
          }
        ]
      }
    }
  },
  methods: {
    onMapReady(map) {
      this.addEvent('Map ready event fired')
      console.log('Map ready:', map)
    },
    onMapChange(map) {
      this.addEvent('Map change event fired')
      console.log('Map change:', map)
    },
    onMarkerClick(data) {
      this.addEvent(`Marker clicked: ${data}`)
      console.log('Marker click:', data)
    },
    onMarkerHover(data) {
      this.addEvent(`Marker hover: ${data || 'null'}`)
      console.log('Marker hover:', data)
    },
    addEvent(message) {
      this.events.unshift(`${new Date().toLocaleTimeString()}: ${message}`)
      if (this.events.length > 50) {
        this.events.pop()
      }
    }
  }
}
</script>

<style scoped>
h2, h3 {
  color: #333;
}
</style>
